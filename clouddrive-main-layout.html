<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive2 - 主界面布局</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .liquid-glass {
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.15) 0%, 
                rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, 
                #667eea 0%, 
                #764ba2 25%, 
                #f093fb 50%, 
                #f5576c 75%, 
                #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .nav-item {
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .nav-item:hover::before {
            left: 100%;
        }
        
        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .scrollbar-hide::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body class="h-screen overflow-hidden gradient-bg">
    <!-- 主容器 -->
    <div class="h-full flex">
        <!-- 左侧导航栏 -->
        <div class="w-64 liquid-glass border-r border-white/20 flex flex-col">
            <!-- Logo区域 -->
            <div class="p-6 border-b border-white/10">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                        <img src="https://unpkg.com/lucide-static@latest/icons/cloud.svg" class="w-5 h-5 text-white" alt="cloud">
                    </div>
                    <h1 class="text-xl font-semibold text-white">CloudDrive</h1>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="p-4">
                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-4 rounded-xl font-medium hover-lift flex items-center justify-center space-x-2">
                    <img src="https://unpkg.com/lucide-static@latest/icons/plus.svg" class="w-4 h-4" alt="plus">
                    <span>新建</span>
                </button>
            </div>
            
            <!-- 导航菜单 -->
            <nav class="flex-1 px-4 space-y-1">
                <div class="nav-item bg-white/10 text-white px-4 py-3 rounded-lg flex items-center space-x-3">
                    <img src="https://unpkg.com/lucide-static@latest/icons/home.svg" class="w-4 h-4" alt="home">
                    <span class="font-medium">首页</span>
                </div>
                
                <div class="nav-item text-white/80 hover:text-white hover:bg-white/5 px-4 py-3 rounded-lg flex items-center space-x-3 cursor-pointer">
                    <img src="https://unpkg.com/lucide-static@latest/icons/star.svg" class="w-4 h-4" alt="star">
                    <span>收藏夹</span>
                </div>
                
                <div class="nav-item text-white/80 hover:text-white hover:bg-white/5 px-4 py-3 rounded-lg flex items-center space-x-3 cursor-pointer">
                    <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" class="w-4 h-4" alt="clock">
                    <span>最近访问</span>
                </div>
                
                <div class="nav-item text-white/80 hover:text-white hover:bg-white/5 px-4 py-3 rounded-lg flex items-center space-x-3 cursor-pointer">
                    <img src="https://unpkg.com/lucide-static@latest/icons/share-2.svg" class="w-4 h-4" alt="share">
                    <span>共享文件</span>
                </div>
                
                <div class="nav-item text-white/80 hover:text-white hover:bg-white/5 px-4 py-3 rounded-lg flex items-center space-x-3 cursor-pointer">
                    <img src="https://unpkg.com/lucide-static@latest/icons/trash-2.svg" class="w-4 h-4" alt="trash">
                    <span>回收站</span>
                </div>
            </nav>
            
            <!-- 存储空间 -->
            <div class="p-4 border-t border-white/10">
                <div class="glass-morphism p-4 rounded-xl">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-white/80 text-sm">存储空间</span>
                        <span class="text-white text-sm font-medium">2.1GB / 15GB</span>
                    </div>
                    <div class="w-full bg-white/20 rounded-full h-2">
                        <div class="bg-gradient-to-r from-blue-400 to-purple-500 h-2 rounded-full" style="width: 14%"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部操作栏 -->
            <header class="liquid-glass border-b border-white/20 px-6 py-4">
                <div class="flex items-center justify-between">
                    <!-- 面包屑导航 -->
                    <div class="flex items-center space-x-2 text-white">
                        <img src="https://unpkg.com/lucide-static@latest/icons/home.svg" class="w-4 h-4" alt="home">
                        <img src="https://unpkg.com/lucide-static@latest/icons/chevron-right.svg" class="w-4 h-4 text-white/60" alt="chevron">
                        <span class="text-white/80">我的文件</span>
                    </div>
                    
                    <!-- 搜索和操作 -->
                    <div class="flex items-center space-x-4">
                        <!-- 搜索框 -->
                        <div class="relative">
                            <input type="text" placeholder="搜索文件..." 
                                   class="glass-morphism text-white placeholder-white/60 px-4 py-2 pl-10 rounded-lg w-64 focus:outline-none focus:ring-2 focus:ring-blue-400/50">
                            <img src="https://unpkg.com/lucide-static@latest/icons/search.svg" 
                                 class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/60" alt="search">
                        </div>
                        
                        <!-- 视图切换 -->
                        <div class="flex items-center glass-morphism rounded-lg p-1">
                            <button class="p-2 bg-white/20 rounded text-white">
                                <img src="https://unpkg.com/lucide-static@latest/icons/grid-3x3.svg" class="w-4 h-4" alt="grid">
                            </button>
                            <button class="p-2 text-white/60 hover:text-white">
                                <img src="https://unpkg.com/lucide-static@latest/icons/list.svg" class="w-4 h-4" alt="list">
                            </button>
                        </div>
                        
                        <!-- 更多操作 -->
                        <button class="glass-morphism p-2 rounded-lg text-white hover-lift">
                            <img src="https://unpkg.com/lucide-static@latest/icons/more-horizontal.svg" class="w-4 h-4" alt="more">
                        </button>
                    </div>
                </div>
            </header>
            
            <!-- 文件内容区域 -->
            <main class="flex-1 p-6 overflow-auto scrollbar-hide">
                <!-- 快速访问 -->
                <section class="mb-8">
                    <h2 class="text-white text-lg font-semibold mb-4">快速访问</h2>
                    <div class="grid grid-cols-4 gap-4">
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mb-3">
                                <img src="https://unpkg.com/lucide-static@latest/icons/folder.svg" class="w-6 h-6 text-white" alt="folder">
                            </div>
                            <h3 class="text-white font-medium">文档</h3>
                            <p class="text-white/60 text-sm">125 个文件</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg flex items-center justify-center mb-3">
                                <img src="https://unpkg.com/lucide-static@latest/icons/image.svg" class="w-6 h-6 text-white" alt="image">
                            </div>
                            <h3 class="text-white font-medium">图片</h3>
                            <p class="text-white/60 text-sm">89 个文件</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mb-3">
                                <img src="https://unpkg.com/lucide-static@latest/icons/video.svg" class="w-6 h-6 text-white" alt="video">
                            </div>
                            <h3 class="text-white font-medium">视频</h3>
                            <p class="text-white/60 text-sm">23 个文件</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-12 h-12 bg-gradient-to-br from-red-400 to-purple-500 rounded-lg flex items-center justify-center mb-3">
                                <img src="https://unpkg.com/lucide-static@latest/icons/music.svg" class="w-6 h-6 text-white" alt="music">
                            </div>
                            <h3 class="text-white font-medium">音乐</h3>
                            <p class="text-white/60 text-sm">156 个文件</p>
                        </div>
                    </div>
                </section>
                
                <!-- 最近文件 -->
                <section>
                    <h2 class="text-white text-lg font-semibold mb-4">最近文件</h2>
                    <div class="file-grid">
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-full h-32 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg mb-3 flex items-center justify-center">
                                <img src="https://unpkg.com/lucide-static@latest/icons/file-text.svg" class="w-8 h-8 text-white" alt="file">
                            </div>
                            <h3 class="text-white font-medium truncate">项目文档.docx</h3>
                            <p class="text-white/60 text-sm">2小时前</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-full h-32 bg-gradient-to-br from-green-400 to-teal-500 rounded-lg mb-3 flex items-center justify-center">
                                <img src="https://unpkg.com/lucide-static@latest/icons/image.svg" class="w-8 h-8 text-white" alt="image">
                            </div>
                            <h3 class="text-white font-medium truncate">设计稿.png</h3>
                            <p class="text-white/60 text-sm">5小时前</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-full h-32 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg mb-3 flex items-center justify-center">
                                <img src="https://unpkg.com/lucide-static@latest/icons/video.svg" class="w-8 h-8 text-white" alt="video">
                            </div>
                            <h3 class="text-white font-medium truncate">演示视频.mp4</h3>
                            <p class="text-white/60 text-sm">1天前</p>
                        </div>
                        
                        <div class="liquid-glass p-4 rounded-xl hover-lift cursor-pointer">
                            <div class="w-full h-32 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg mb-3 flex items-center justify-center">
                                <img src="https://unpkg.com/lucide-static@latest/icons/folder.svg" class="w-8 h-8 text-white" alt="folder">
                            </div>
                            <h3 class="text-white font-medium truncate">工作文件夹</h3>
                            <p class="text-white/60 text-sm">3天前</p>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>
</body>
</html>
